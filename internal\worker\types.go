package worker

import (
	"context"
	"runtime"
	"sync"

	"github.com/amankumarsingh77/cloud-video-encoder/internal/config"
	"github.com/amankumarsingh77/cloud-video-encoder/internal/models"
	"github.com/amankumarsingh77/cloud-video-encoder/internal/videofiles"
	"github.com/amankumarsingh77/cloud-video-encoder/pkg/logger"
	"github.com/google/uuid"
)

const (
	VideoJobsQueueKey     = "video_jobs"
	TempDir               = "tmp_segments"
	MinSegmentDuration    = 6
	MaxSegments           = 16
	DefaultBaseBitrate    = 400
	HDBaseBitrate         = 800
	FullHDBaseBitrate     = 1500
	MaxConcurrentUploads  = 50
	MaxConcurrentDownloads = 10
)

type Worker struct {
	logger    logger.Logger
	redisRepo videofiles.RedisRepository
	awsRepo   videofiles.AWSRepository
	videoRepo videofiles.Repository
	cfg       *config.Config
	stopChan  chan struct{}
	wg        sync.WaitGroup
	jobs      chan *models.EncodeJob
	semaphore chan struct{}
}

type VideoInfo struct {
	Width    int
	Height   int
	Duration float64
}

type VideoProcessor interface {
	ProcessVideo(ctx context.Context, job *models.EncodeJob, videoID uuid.UUID) (*ProcessingResult, error)
}

func GetOptimalParallelJobs() int {
	cores := runtime.NumCPU()
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	availableMemoryGB := float64(memStats.Sys) / (1024 * 1024 * 1024)

	memoryBasedLimit := int(availableMemoryGB / 2)
	if memoryBasedLimit < 1 {
		memoryBasedLimit = 1
	}

	coreBasedLimit := cores
	switch {
	case cores >= 16:
		coreBasedLimit = cores - 2
	case cores >= 8:
		coreBasedLimit = cores - 1
	case cores >= 4:
		coreBasedLimit = cores
	default:
		coreBasedLimit = 2
	}

	if memoryBasedLimit < coreBasedLimit {
		return memoryBasedLimit
	}
	return coreBasedLimit
}
